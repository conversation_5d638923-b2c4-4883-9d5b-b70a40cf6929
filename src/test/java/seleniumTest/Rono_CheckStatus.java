 package seleniumTest;

public class Rono_CheckStatus extends Rono_Names {

    public void checkStatus() {

        if (harshini_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(harshini_string)) {
                harshini_amount = restbetAmount;
                harshini_samecount = harshini_samecount + 1;
                harshini_diffcount = 0;


            } else {
                harshini_amount = harshini_amount * 2;
                harshini_diffcount = harshini_diffcount + 1;
                harshini_samecount = 0;


            }

            harshini_performed = 0;

        }

        if (dune_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(dune_string)) {
                dune_amount = restbetAmount;
                dune_same = dune_same + 1;
                dune_diff = 0;
            } else {
                dune_amount = dune_amount * 2;
                dune_diff = dune_diff + 1;
                dune_same = 0;
            }

            dune_performed = 0;
        }

        if (coco_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(coco_string)) {
                coco_amount = restbetAmount;
                coco_same = coco_same + 1;
                coco_diff = 0;

            } else {
                coco_amount = coco_amount * 2;
                coco_diff = coco_diff + 1;
                coco_same = 0;

            }

            coco_performed = 0;
        }

        if (ratatouille_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(ratatouille_string)) {

                ratatouille_amount = restbetAmount;
                ratatouille_same = ratatouille_same + 1;
                ratatouille_diff = 0;

            } else {

                ratatouille_amount = ratatouille_amount * 2;
                ratatouille_diff = ratatouille_diff + 1;
                ratatouille_same = 0;

            }

            ratatouille_performed = 0;
        }

        if (toyStory_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(toyStory_string)) {
                toyStory_amount = restbetAmount;
                toyStory_same = toyStory_same + 1;
                toyStory_diff = 0;

            } else {
                toyStory_amount = toyStory_amount * 2;
                toyStory_diff = toyStory_diff + 1;
                toyStory_same = 0;

            }

            toyStory_performed = 0;
        }

        if (shrek_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(shrek_string)) {
                shrek_amount = restbetAmount;
                shrek_same = shrek_same + 1;
                shrek_diff = 0;
                if (total > (index + 1)) {
                    index++;
                } else {
                    index = 0;
                }
            } else {
                shrek_amount = shrek_amount * 2;
                shrek_diff = shrek_diff + 1;
                shrek_same = 0;
                index = 0;
            }

            shrek_performed = 0;
        }

        if (lordRings_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(lordRings_string)) {
                lordRings_amount = restbetAmount;
                lordRings_same = lordRings_same + 1;
                lordRings_diff = 0;
            } else {
                lordRings_amount = lordRings_amount * 2;
                lordRings_diff = lordRings_diff + 1;
                lordRings_same = 0;
            }

            lordRings_performed = 0;
        }
        if (brave_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(brave_string)) {
                brave_amount = restbetAmount;
                brave_same = brave_same + 1;
                brave_diff = 0;

            } else {
                brave_amount = brave_amount * 2;
                brave_diff = brave_diff + 1;
                brave_same = 0;

            }

            brave_performed = 0;
        }

        if (cars_performed == 1) {
            String resultString = currentresult;

            if (resultString.equals(cars_string)) {
                cars_same = cars_same + 1;
                cars_diff = 0;
                cars_amount = restbetAmount;

            } else {
                cars_amount = cars_amount * 2;
                cars_diff = cars_diff + 1;
                cars_same = 0;

            }

            cars_performed = 0;
        }

        if (bigHero6_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(bigHero6_string)) {
                bigHero6_amount = restbetAmount;
                bigHero6_same = bigHero6_same + 1;
                bigHero6_diff = 0;




            } else {
                bigHero6_amount = bigHero6_amount * 2;
                bigHero6_diff = bigHero6_diff + 1;
                bigHero6_same = 0;

            }

            bigHero6_performed = 0;
        }
        if (wallE_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(wallE_string)) {
                wallE_amount = restbetAmount;
                wallE_same = wallE_same + 1;
                wallE_diff = 0;

            } else {
                wallE_amount = wallE_amount * 2;
                wallE_diff = wallE_diff + 1;
                wallE_same = 0;

            }

            wallE_performed = 0;
        }
        if (findingNemo_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(findingNemo_string)) {
                findingNemo_amount = restbetAmount;
                findingNemo_same = findingNemo_same + 1;
                findingNemo_diff = 0;

            } else {
                findingNemo_amount = findingNemo_amount * 2;
                findingNemo_diff = findingNemo_diff + 1;
                findingNemo_same = 0;

            }

            findingNemo_performed = 0;
        }

        if (soul_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(soul_string)) {
                soul_amount = restbetAmount;
                soul_same = soul_same + 1;
                soul_diff = 0;

            } else {
                soul_amount = soul_amount * 2;
                soul_diff = soul_diff + 1;
                soul_same = 0;

            }

            soul_performed = 0;
        }

        if (encanto_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(encanto_string)) {
                encanto_amount = restbetAmount;
                encanto_same = encanto_same + 1;
                encanto_diff = 0;

            } else {
                encanto_amount = encanto_amount * 2;
                encanto_diff = encanto_diff + 1;
                encanto_same = 0;

            }

            encanto_performed = 0;
        }

        if (hobbit_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(hobbit_string)) {
                hobbit_amount = restbetAmount;
                hobbit_same = hobbit_same + 1;
                hobbit_diff = 0;
            } else {
                hobbit_amount = hobbit_amount * 2;
                hobbit_diff = hobbit_diff + 1;
                hobbit_same = 0;
            }

            hobbit_performed = 0;
        }

        if (parasite_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(parasite_string)) {
                parasite_amount = restbetAmount;
                parasite_same = parasite_same + 1;
                parasite_diff = 0;
            } else {
                parasite_amount = parasite_amount * 2;
                parasite_diff = parasite_diff + 1;
                parasite_same = 0;
            }

            parasite_performed = 0;
        }

        if (mulan_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(mulan_string)) {
                mulan_amount = restbetAmount;
                mulan_same = mulan_same + 1;
                mulan_diff = 0;

            } else {
                mulan_amount = mulan_amount * 2;
                mulan_diff = mulan_diff + 1;
                mulan_same = 0;

            }

            mulan_performed = 0;
        }

        if (hercules_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(hercules_string)) {
                hercules_amount = restbetAmount;
                hercules_same = hercules_same + 1;
                hercules_diff = 0;

            } else {
                hercules_amount = hercules_amount * 2;
                hercules_diff = hercules_diff + 1;
                hercules_same = 0;

            }

            hercules_performed = 0;
        }

        if (lionKing_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(lionKing_string)) {
                lionKing_amount = restbetAmount;
                lionKing_same = lionKing_same + 1;
                lionKing_diff = 0;

            } else {
                lionKing_amount = lionKing_amount * 2;
                lionKing_diff = lionKing_diff + 1;
                lionKing_same = 0;

            }

            lionKing_performed = 0;
        }

        if (whiplash_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(whiplash_string)) {
                whiplash_amount = restbetAmount;
                whiplash_same = whiplash_same + 1;
                whiplash_diff = 0;
            } else {
                whiplash_amount = whiplash_amount * 2;
                whiplash_diff = whiplash_diff + 1;
                whiplash_same = 0;
            }

            whiplash_performed = 0;
        }

        if (cloudyMeatballs_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(cloudyMeatballs_string)) {
                cloudyMeatballs_amount = restbetAmount;
                cloudyMeatballs_same = cloudyMeatballs_same + 1;
                cloudyMeatballs_diff = 0;
            } else {
                cloudyMeatballs_amount = cloudyMeatballs_amount * 2;
                cloudyMeatballs_diff = cloudyMeatballs_diff + 1;
                cloudyMeatballs_same = 0;
            }

            cloudyMeatballs_performed = 0;
        }

        if (emojiMovie_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(emojiMovie_string)) {
                emojiMovie_amount = restbetAmount;
                emojiMovie_same = emojiMovie_same + 1;
                emojiMovie_diff = 0;
            } else {
                emojiMovie_amount = emojiMovie_amount * 2;
                emojiMovie_diff = emojiMovie_diff + 1;
                emojiMovie_same = 0;
            }

            emojiMovie_performed = 0;
        }

        if (hotelTransylvania_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(hotelTransylvania_string)) {
                hotelTransylvania_amount = restbetAmount;
                hotelTransylvania_same = hotelTransylvania_same + 1;
                hotelTransylvania_diff = 0;
            } else {
                hotelTransylvania_amount = hotelTransylvania_amount * 2;
                hotelTransylvania_diff = hotelTransylvania_diff + 1;
                hotelTransylvania_same = 0;
            }

            hotelTransylvania_performed = 0;
        }
        if (godfather_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(godfather_string)) {
                godfather_amount = restbetAmount;
                godfather_same = godfather_same + 1;
                godfather_diff = 0;
            } else {
                godfather_amount = godfather_amount * 2;
                godfather_diff = godfather_diff + 1;
                godfather_same = 0;
            }

            godfather_performed = 0;
        }

        if (minions_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(minions_string)) {
                minions_amount = restbetAmount;
                minions_same = minions_same + 1;
                minions_diff = 0;
            } else {
                minions_amount = minions_amount * 2;
                minions_diff = minions_diff + 1;
                minions_same = 0;
            }

            minions_performed = 0;
        }

        if (madagascar_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(madagascar_string)) {
                madagascar_amount = restbetAmount;
                madagascar_same = madagascar_same + 1;
                madagascar_diff = 0;
            } else {
                madagascar_amount = madagascar_amount * 2;
                madagascar_diff = madagascar_diff + 1;
                madagascar_same = 0;
            }

            madagascar_performed = 0;
        }

        if (iceAge_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(iceAge_string)) {
                iceAge_amount = restbetAmount;
                iceAge_same = iceAge_same + 1;
                iceAge_diff = 0;
            } else {
                iceAge_amount = iceAge_amount * 2;
                iceAge_diff = iceAge_diff + 1;
                iceAge_same = 0;
            }

            iceAge_performed = 0;
        }

        if (django_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(django_string)) {
                django_amount = restbetAmount;
                django_same = django_same + 1;
                django_diff = 0;
            } else {
                django_amount = django_amount * 2;
                django_diff = django_diff + 1;
                django_same = 0;
            }

            django_performed = 0;
        }

        if (killBill_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(killBill_string)) {
                killBill_amount = restbetAmount;
                killBill_same = killBill_same + 1;
                killBill_diff = 0;
            } else {
                killBill_amount = killBill_amount * 2;
                killBill_diff = killBill_diff + 1;
                killBill_same = 0;
            }

            killBill_performed = 0;
        }
        if (wonderWoman_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(wonderWoman_string)) {
                wonderWoman_amount = restbetAmount;
                wonderWoman_same = wonderWoman_same + 1;
                wonderWoman_diff = 0;
            } else {
                wonderWoman_amount = wonderWoman_amount * 2;
                wonderWoman_diff = wonderWoman_diff + 1;
                wonderWoman_same = 0;
            }

            wonderWoman_performed = 0;
        }
        if (aquaman_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(aquaman_string)) {
                aquaman_amount = restbetAmount;
                aquaman_same = aquaman_same + 1;
                aquaman_diff = 0;
            } else {
                aquaman_amount = aquaman_amount * 2;
                aquaman_diff = aquaman_diff + 1;
                aquaman_same = 0;
            }

            aquaman_performed = 0;
        }
        if (superman_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(superman_string)) {
                superman_amount = restbetAmount;
                superman_same = superman_same + 1;
                superman_diff = 0;
            } else {
                superman_amount = superman_amount * 2;
                superman_diff = superman_diff + 1;
                superman_same = 0;
            }

            superman_performed = 0;
        }
        if (batman_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(batman_string)) {
                batman_amount = restbetAmount;
                batman_same = batman_same + 1;
                batman_diff = 0;
            } else {
                batman_amount = batman_amount * 2;
                batman_diff = batman_diff + 1;
                batman_same = 0;
            }

            batman_performed = 0;
        }

        if (hatefulEight_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(hatefulEight_string)) {
                hatefulEight_amount = restbetAmount;
                hatefulEight_same = hatefulEight_same + 1;
                hatefulEight_diff = 0;
            } else {
                hatefulEight_amount = hatefulEight_amount * 2;
                hatefulEight_diff = hatefulEight_diff + 1;
                hatefulEight_same = 0;
            }

            hatefulEight_performed = 0;
        }


        if (despicableMe_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(despicableMe_string)) {
                despicableMe_amount = restbetAmount;
                despicableMe_same = despicableMe_same + 1;
                despicableMe_diff = 0;
            } else {
                despicableMe_amount = despicableMe_amount * 2;
                despicableMe_diff = despicableMe_diff + 1;
                despicableMe_same = 0;
            }

            despicableMe_performed = 0;
        }


        if (sing_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(sing_string)) {
                sing_amount = restbetAmount;
                sing_same = sing_same + 1;
                sing_diff = 0;
            } else {
                sing_amount = sing_amount * 2;
                sing_diff = sing_diff + 1;
                sing_same = 0;
            }

            sing_performed = 0;
        }

        if (pulpFiction_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(pulpFiction_string)) {
                pulpFiction_amount = restbetAmount;
                pulpFiction_same = pulpFiction_same + 1;
                pulpFiction_diff = 0;
            } else {
                pulpFiction_amount = pulpFiction_amount * 2;
                pulpFiction_diff = pulpFiction_diff + 1;
                pulpFiction_same = 0;
            }

            pulpFiction_performed = 0;
        }

        if (megamind_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(megamind_string)) {
                megamind_amount = restbetAmount;
                megamind_same = megamind_same + 1;
                megamind_diff = 0;
            } else {
                megamind_amount = megamind_amount * 2;
                megamind_diff = megamind_diff + 1;
                megamind_same = 0;
            }

            megamind_performed = 0;
        }

        if (smurfs_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(smurfs_string)) {
                smurfs_amount = restbetAmount;
                smurfs_same = smurfs_same + 1;
                smurfs_diff = 0;
            } else {
                smurfs_amount = smurfs_amount * 2;
                smurfs_diff = smurfs_diff + 1;
                smurfs_same = 0;
            }

            smurfs_performed = 0;
        }


        if (goodfellas_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(goodfellas_string)) {
                goodfellas_amount = restbetAmount;
                goodfellas_same = goodfellas_same + 1;
                goodfellas_diff = 0;
            } else {
                goodfellas_amount = goodfellas_amount * 2;
                goodfellas_diff = goodfellas_diff + 1;
                goodfellas_same = 0;
            }

            goodfellas_performed = 0;
        }


        if (laLaLand_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(laLaLand_string)) {
                laLaLand_amount = restbetAmount;
                laLaLand_same = laLaLand_same + 1;
                laLaLand_diff = 0;
            } else {
                laLaLand_amount = laLaLand_amount * 2;
                laLaLand_diff = laLaLand_diff + 1;
                laLaLand_same = 0;
            }

            laLaLand_performed = 0;
        }

        if (frozen2_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(frozen2_string)) {
                frozen2_amount = restbetAmount;
                frozen2_same = frozen2_same + 1;
                frozen2_diff = 0;

            } else {
                frozen2_amount = frozen2_amount * 2;
                frozen2_diff = frozen2_diff + 1;
                frozen2_same = 0;

            }

            frozen2_performed = 0;
        }

        if (beautyAndBeast_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(beautyAndBeast_string)) {
                beautyAndBeast_amount = restbetAmount;
                beautyAndBeast_same = beautyAndBeast_same + 1;
                beautyAndBeast_diff = 0;


            } else {
                beautyAndBeast_amount = beautyAndBeast_amount * 2;
                beautyAndBeast_diff = beautyAndBeast_diff + 1;
                beautyAndBeast_same = 0;

            }

            beautyAndBeast_performed = 0;
        }

        if (dunkirk_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(dunkirk_string)) {
                dunkirk_amount = restbetAmount;
                dunkirk_same = dunkirk_same + 1;
                dunkirk_diff = 0;
            } else {
                dunkirk_amount = dunkirk_amount * 2;
                dunkirk_diff = dunkirk_diff + 1;
                dunkirk_same = 0;
            }

            dunkirk_performed = 0;
        }
        if (aladdin_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(aladdin_string)) {
                aladdin_amount = restbetAmount;
                aladdin_same = aladdin_same + 1;
                aladdin_diff = 0;
            } else {
                aladdin_amount = aladdin_amount * 2;
                aladdin_diff = aladdin_diff + 1;
                aladdin_same = 0;
            }

            aladdin_performed = 0;
        }


        if (tenet_performed == 1) {
            String resultString = currentresult;
            if (resultString.equals(tenet_string)) {
                tenet_amount = restbetAmount;
                tenet_samecount = tenet_samecount + 1;
                tenet_diffcount = 0;
            } else {
                tenet_amount = tenet_amount * 2;
                tenet_diffcount = tenet_diffcount + 1;
                tenet_samecount = 0;
            }

            tenet_performed = 0;
        }


    }
}