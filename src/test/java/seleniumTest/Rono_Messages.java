package seleniumTest;

import java.io.IOException;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;

public class Rono_Messages {
    private static final String BOT_TOKEN = "6876974494:AAEE2mzOfWN4xPpfpxSz-Mq_6GR5qzaKgYk";
    private static final String CHAT_ID = "5314994775";

    private static final String BOT_TOKEN_kishore = "7601000581:AAGqxd_3urKjUiXLhRHES2wJInRKPhBuezM";
    private static final String BOT_TOKEN_Harshini = "8174925339:AAE-m5xFy1FDmjyNjqu_HSu229fOo20qhW8";
    private static final String BOT_TOKEN_min32 = "6771560261:AAGcCIcA4dp7UXMtugSOf1caUGKj7P_DGK4";
    private static final String BOT_TOKEN_min64 = "7268376014:AAEJxdObFr6vQRU03WCXxI7DUhseQv3r8WA";
    private static final String BOT_TOKEN_min16 = "7202763084:AAHJ3wWprmTbg8KCq4elmSabSnTPf1dA8lE";
    private static final String BOT_TOKEN_min8 = "7272145560:AAGrO_1BT2Vwe30LsZQ6ndIKUye8nPPXK0Y";
    private static final String BOT_TOKEN_min4 = "7515542904:AAHKWNBNMrc8ND7sPnFU1Zpq2lAskRXjhMg";
    private static final String BOT_TOKEN_albin = "6930650280:AAGF2u8Zod2otbNSX73f0jabXb-iS_H4LKo";
    private static final String BOT_TOKEN_min2 = "7516611597:AAH9IILUsLxoA2zpyUqy4bfKhzj0S-hVK70";
    private static final String BOT_TOKEN_suresh = "6876974494:AAEE2mzOfWN4xPpfpxSz-Mq_6GR5qzaKgYk";
    private static final String BOT_TOKEN_padma = "7037357380:AAEfAoCOdKAfecOs1IRLjZYJvYan00tDV34";
    private static final String BOT_TOKEN_chinchu = "7471655190:AAF43dAm1X5s61WQiTnA8_OWjq8GYjfVMLk";
    private static final String BOT_TOKEN_Obuli = "7385164818:AAGiQkqVF8RVbsyauw_G3cDAUbKFmrT8Ub4";

    private static final String BOT_TOKEN_Divi1 = "7840193036:AAHmKdtHfCTHMzawwdBO1ldbX20TR8xDmV4";
    private static final String BOT_TOKEN_Divi2 = "7790062803:AAHpRJkB1zLbMDcXW1BGO8KLkmnmUFGDXyw";
    private static final String BOT_TOKEN_Divi3 = "7645433098:AAG4m3uEk831RVCxVFwEodKsjznKMPKCkuY";
    private static final String BOT_TOKEN_Divi4 = "7889476845:AAF8pLaIXIl09k5uUOg-FfatFOiaX2I_rc8";
    private static final String BOT_TOKEN_Divi5 = "7594274644:AAFkd0NTI9JoYj9hRD8ppNoSiEkaJ2hqII0";
    private static final String BOT_TOKEN_Divi6 = "7405354903:AAEdAl7KjaHrVcoCaonr5AMr7iFGry3Q16k";
    private static final String BOT_TOKEN_Divi7 = "7817024522:AAFBIVO8js-XZmyEdzLJvogGD2YVAoa4YwI";
    private static final String BOT_TOKEN_Divi8 = "7774644807:AAHMLBQmFCqvASVkFUTtDBAwRngVKCfJxtE";
    private static final String BOT_TOKEN_Divi9 = "7520928241:AAEVRIsiUWeEtiD2_cw5ie9nHap2TOCdBNU";
    private static final String BOT_TOKEN_Divi10 = "7689844551:AAGAXQWNGqJzGnppnGWiFZk4taUxp3HIwuU";

    private static final String BOT_TOKEN_Ramu1 = "8023723120:AAH2GehXsuvPY9YAhi0QxBRCKo96xJd-egk";
    private static final String BOT_TOKEN_Ramu2 = "7608262070:AAH-iTDUITzCrI3LYtK66Os1Wy4uO_qTLVs";
    private static final String BOT_TOKEN_Ramu3 = "7553300436:AAHH5viOKDv95Ot68Oe329s3Iend7OjihQ0";
    private static final String BOT_TOKEN_Ramu4 = "8133512327:AAH_r6cBaLoZZ3X1Q2KbB5pdq6VWpWbPsaM";
    private static final String BOT_TOKEN_Ramu5 = "8145583052:AAH3k3x5pZ18qVsVXzv1fEIsoItXT2CUv8w";
    private static final String BOT_TOKEN_Ramu6 = "7682467620:AAGg27uKAy9cSc4AmcEQPgVwN6_49KVVySE";
    private static final String BOT_TOKEN_Ramu7 = "7404168953:AAGOo9v2UJwC0gpTQZYKqpe9q9SzVQqu33o";

    private static final String BOT_TOKEN_padma1 = "8179425027:AAHPF6s-vOZlicL97UR7_Cwev1JFjECazvk";
    private static final String BOT_TOKEN_padma2 = "7831186183:AAG-ValeG_hmbNSkEBJNBuwMquRLnWJDYL8";
    private static final String BOT_TOKEN_padma4 = "7182314207:AAGVO1JiuvLyBHQU7kmkIk5iobLgCAEeQDY";
    private static final String BOT_TOKEN_padma8 = "7927402366:AAHiPEW1XJpvDnBwl57yqHQrZrPeH1S8miE";
    private static final String BOT_TOKEN_padma16 = "7877625647:AAF8xjoWtCll-1xWSsdnB8-aP2MOHFc5JTw";

    private static final String BOT_TOKEN_Har_obuli1 = "8113599464:AAHfpVnEGoxhxLqw2CVSyjBjLRmGmsi5tj8";
    private static final String BOT_TOKEN_Har_obuli2 = "7761667005:AAF5qqRaxYhkU2OotzUrZfVxKJYCJVVRspc";
    private static final String BOT_TOKEN_Har_obuli4 = "7459255600:AAE8G4jVOe3x1xQtk_eMXTX4ggK5ndgZzu8";
    private static final String BOT_TOKEN_Har_obuli8 = "7480632823:AAEhpfTDzBylJuZikqRVHQhu7LPbQy-oWoY";
    private static final String BOT_TOKEN_Har_obuli16 = "7780383750:AAGDFjIzRVNr4rspm7tc9AsK3tujjzNOjr0";
    private static final String BOT_TOKEN_Har_obuli32 = "7365854711:AAGby2GvLnmHUlbhamX4qoiSF1sd-kt6R0A";

    private static final String BOT_TOKEN_Har_min1 = "7453956521:AAG5Ptch2xNkV0sARKqCLGIrPjx2B3SRXOc";
    private static final String BOT_TOKEN_Har_min2 = "8129663559:AAGuB-NjER4_kC_Lpkvix94CVIYaiQb1LwY";
    private static final String BOT_TOKEN_Har_min4 = "7786250974:AAEiWtNiVNV7DGmgynnzUuKYexyqMS2fdsg";
    private static final String BOT_TOKEN_Har_min8 = "7209638780:AAF_jSLLtfbxq_7l17m5ePLDPCN-QY0lXN4";
    private static final String BOT_TOKEN_Har_min16 = "7955275315:AAE3QJ2YAPCoX68FvulEHhaSLX3aLu5Gh-M";
    private static final String BOT_TOKEN_Har_min32 = "7268376014:AAHgGnd6pfrKBaCpLckPh-9nWrhpoAjrwQo";

    private static final String BOT_TOKEN_Last2 = "7266466174:AAF8iiGiFpvhqoSFpIh8gfSdiq8Q4kq8TfQ";
    private static final String BOT_TOKEN_Last4 = "7876611631:AAEhlygViXq9ifRCuyB4cbjmE-N_0eWMmYg";
    private static final String BOT_TOKEN_Last8 = "7311649652:AAHJIYGOW6jiY0_plaRVG0Qe8-Ixa5PsFpc";
    private static final String BOT_TOKEN_Last16 = "7426460807:AAEVMRqbtef9zPzUg6rFIPTabPxmSHC2dHI";
    private static final String BOT_TOKEN_Last32 = "6589324494:AAF_HKGvQYq3g5eIjjbwCecLGif5SwE9fNQ";
    private static final String BOT_TOKEN_Last64 = "7707572077:AAFIX1rj-llVICThbASHdsD0RHdfjlSHYCA";

    private static final String BOT_TOKEN_kis1 = "8197624310:AAE9HoN983vFVzgmjWbnkXyWj9t42O65cbY";
    private static final String BOT_TOKEN_kis2 = "8032396108:AAHPWBSiBGSKdJFZuwrIwtwYNan9MP6NBBs";
    private static final String BOT_TOKEN_kis4 = "8057475082:AAHCyTbDzmZ8ap1mZFYvu9xEVex8-Q0IBUw";
    private static final String BOT_TOKEN_kis8 = "7747161477:AAHXoKlC_c6JWnBkb7UaEhj8wEVG7KcxkqM";
    private static final String BOT_TOKEN_kis16 = "7667356989:AAEl5uKJPgDOB6GSW9gbFYv_60LQD9diMaE";

    private static final String BOT_TOKEN_Rono1 = "7868212434:AAFxW_rZDFkNZ9gVPUDvwzmqL5MmZib9dn4";
    private static final String BOT_TOKEN_Rono2 = "7432364878:AAGPkb6csCseIaxFyUTh9P9e6LMqlu9jzo4";
    private static final String BOT_TOKEN_Rono3 = "7669711640:AAFeZq1sw6fmaS0KGXajNnDT93Z4TjF6gVM";
    private static final String BOT_TOKEN_Rono4 = "7830048306:AAFDY5k_x0jRgjzVnKW-B9J66CZGse2TPBs";
    private static final String BOT_TOKEN_Rono5 = "7931752885:AAFjWmcCpWG65Th6z7dzeT00GE1IniAabsc";
    private static final String BOT_TOKEN_Rono6 = "7661715575:AAHdb2vW20VqDkJdsBWpuuTUtjIjxJiEz1U";
    private static final String BOT_TOKEN_Rono7 = "7889631737:AAH4IGyWr2R7XAIw3irVU2vlwi4QQovZkco";
    private static final String BOT_TOKEN_Rono8 = "8067001513:AAHzaCeXcPmOXMvyHakv7Ek4zVNpyxl2wDo";
    private static final String BOT_TOKEN_Rono9 = "7556380830:AAGD5uC65MF-apA2dGJFdCR9SDmAO2OJpTY";
    private static final String BOT_TOKEN_Rono10 = "7420860905:AAF8ICq-YBOCT908TrcIsuKiuMqRgXTw9Hw";
    private static final String BOT_TOKEN_Rono11 = "7661951329:AAGm2-qZQgqD5eIuJ1v5P3vBA7vp8XRWb0k";
    private static final String BOT_TOKEN_Rono12 = "7809730912:AAFABr66J_-Da67ppsP49ifIEfCyjRyhSkA";
    private static final String BOT_TOKEN_Rono13 = "7807690388:AAE-01fK83lujTsruH3dGMLzL9n9Dxc6TOc";
    private static final String BOT_TOKEN_Rono14 = "7788757628:AAGi3PGTkhqI3ZoC0R8Kb0D17B5GaxDZOqM";
    private static final String BOT_TOKEN_Rono15 = "8057719639:AAGPJgebzvdr9nXm7a-YyvMAaQT63ZJ07rQ";
    private static final String BOT_TOKEN_Rono16 = "7336479238:AAEg9vRFxRQqgjbR-CZBLFmIpDjO7tUDWgw";
    private static final String BOT_TOKEN_Rono17 = "7483330380:AAH4vFBxPxOkQ5KyZZUQgd-som4s7Wryf3U";
    private static final String BOT_TOKEN_Rono18 = "7851475957:AAFWlkKL4h_mIpMXmVT8v9z34jEDaD3Zqa0";
    private static final String BOT_TOKEN_Rono19 = "7570043554:AAEuGL0L9gIe6TTPhoEcmbrEzT0jCuvhxyI";
    private static final String BOT_TOKEN_Rono20 = "7123390095:AAGNsG3VwjujoxXSocLy8FOyczuKdA1UBAQ";

    public static void sendMessage(String message) {
        sendTelegramMessage(BOT_TOKEN, message);
    }

    public static void sendMessageKishore(String message) {
        sendTelegramMessage(BOT_TOKEN_kishore, message);
    }
    public static void sendMessageHarshini(String message) {
        sendTelegramMessage(BOT_TOKEN_Harshini, message);
    }
    public static void sendMessageRono1(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono1, message);
    }
    public static void sendMessageRono2(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono2, message);
    }
    public static void sendMessageRono3(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono3, message);
    }
    public static void sendMessageRono4(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono4, message);
    }
    public static void sendMessageRono5(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono5, message);
    }
    public static void sendMessageRono6(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono6, message);
    }
    public static void sendMessageRono7(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono7, message);
    }
    public static void sendMessageRono8(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono8, message);
    }
    public static void sendMessageRono9(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono9, message);
    }
    public static void sendMessageRono10(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono10, message);
    }
    public static void sendMessageRono11(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono11, message);
    }
    public static void sendMessageRono12(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono12, message);
    }
    public static void sendMessageRono13(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono13, message);
    }
    public static void sendMessageRono14(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono14, message);
    }
    public static void sendMessageRono15(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono15, message);
    }
    public static void sendMessageRono16(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono16, message);
    }
    public static void sendMessageRono17(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono17, message);
    }
    public static void sendMessageRono18(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono18, message);
    }
    public static void sendMessageRono19(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono19, message);
    }
    public static void sendMessageRono20(String message) {
        sendTelegramMessage(BOT_TOKEN_Rono20, message);
    }

    public static void sendMessage32(String message) {
        sendTelegramMessage(BOT_TOKEN_min32, message);
    }
    public static void sendMessage64(String message) {
        sendTelegramMessage(BOT_TOKEN_min64, message);
    }
    public static void sendMessage16(String message) {
        sendTelegramMessage(BOT_TOKEN_min16, message);
    }
    public static void sendMessage8(String message) {
        sendTelegramMessage(BOT_TOKEN_min8, message);
    }
    public static void sendMessage4(String message) {
        sendTelegramMessage(BOT_TOKEN_min4, message);
    }
    public static void sendMessage2(String message) {
        sendTelegramMessage(BOT_TOKEN_min2, message);
    }
    public static void sendMessagealbin(String message) {
        sendTelegramMessage(BOT_TOKEN_albin, message);
    }

    public static void sendMessageSuresh(String message) {
        sendTelegramMessage(BOT_TOKEN_suresh, message);
    }
    public static void sendMessagePadma(String message) {
        sendTelegramMessage(BOT_TOKEN_padma, message);
    }
    public static void sendMessageChinchu(String message) {
        sendTelegramMessage(BOT_TOKEN_chinchu, message);
    }

    public static void sendMessageObuli(String message) {
        sendTelegramMessage(BOT_TOKEN_Obuli, message);
    }

    public static void sendMessagekis1(String message) {
        sendTelegramMessage(BOT_TOKEN_kis1, message);
    }
    public static void sendMessagekis2(String message) {
        sendTelegramMessage(BOT_TOKEN_kis2, message);
    }
    public static void sendMessagekis4(String message) {
        sendTelegramMessage(BOT_TOKEN_kis4, message);
    }
    public static void sendMessagekis8(String message) {
        sendTelegramMessage(BOT_TOKEN_kis8, message);
    }
    public static void sendMessagekis16(String message) {
        sendTelegramMessage(BOT_TOKEN_kis16, message);
    }

    public static void sendMessagelast2(String message) {
        sendTelegramMessage(BOT_TOKEN_Last2, message);
    }
    public static void sendMessagelast4(String message) {
        sendTelegramMessage(BOT_TOKEN_Last4, message);
    }
    public static void sendMessagelast8(String message) {
        sendTelegramMessage(BOT_TOKEN_Last8, message);
    }
    public static void sendMessagelast16(String message) {
        sendTelegramMessage(BOT_TOKEN_Last16, message);
    }
    public static void sendMessagelast32(String message) {
        sendTelegramMessage(BOT_TOKEN_Last32, message);
    }
    public static void sendMessagelast64(String message) {
        sendTelegramMessage(BOT_TOKEN_Last64, message);
    }

    public static void sendMessagehar1(String message) {
        sendTelegramMessage(BOT_TOKEN_Har_min1, message);
    }
    public static void sendMessagehar2(String message) {
        sendTelegramMessage(BOT_TOKEN_Har_min2, message);
    }
    public static void sendMessagehar4(String message) {
        sendTelegramMessage(BOT_TOKEN_Har_min4, message);
    }
    public static void sendMessagehar8(String message) {
        sendTelegramMessage(BOT_TOKEN_Har_min8, message);
    }
    public static void sendMessagehar16(String message) {
        sendTelegramMessage(BOT_TOKEN_Har_min16, message);
    }
    public static void sendMessagehar32(String message) {
        sendTelegramMessage(BOT_TOKEN_Har_min32, message);
    }


    public static void sendMessageobuli1(String message) {
        sendTelegramMessage(BOT_TOKEN_Har_obuli1, message);
    }

    public static void sendMessageobuli2(String message) {
        sendTelegramMessage(BOT_TOKEN_Har_obuli2, message);
    }
    public static void sendMessageobuli4(String message) {
        sendTelegramMessage(BOT_TOKEN_Har_obuli4, message);
    }
    public static void sendMessageobuli8(String message) {
        sendTelegramMessage(BOT_TOKEN_Har_obuli8, message);
    }
    public static void sendMessageobuli16(String message) {
        sendTelegramMessage(BOT_TOKEN_Har_obuli16, message);
    }
    public static void sendMessageobuli32(String message) {
        sendTelegramMessage(BOT_TOKEN_Har_obuli32, message);
    }

    public static void sendMessagePadma1(String message) {
        sendTelegramMessage(BOT_TOKEN_padma1, message);
    }
    public static void sendMessagePadma2(String message) {
        sendTelegramMessage(BOT_TOKEN_padma2, message);
    }
    public static void sendMessagePadma4(String message) {
        sendTelegramMessage(BOT_TOKEN_padma4, message);
    }
    public static void sendMessagePadma8(String message) {
        sendTelegramMessage(BOT_TOKEN_padma8, message);
    }
    public static void sendMessagePadma16(String message) {
        sendTelegramMessage(BOT_TOKEN_padma16, message);
    }


    public static void sendMessageRamu1(String message) {
        sendTelegramMessage(BOT_TOKEN_Ramu1, message);
    }
    public static void sendMessageRamu2(String message) {
        sendTelegramMessage(BOT_TOKEN_Ramu2, message);
    }
    public static void sendMessageRamu3(String message) {
        sendTelegramMessage(BOT_TOKEN_Ramu3, message);
    }
    public static void sendMessageRamu4(String message) {
        sendTelegramMessage(BOT_TOKEN_Ramu4, message);
    }
    public static void sendMessageRamu5(String message) {
        sendTelegramMessage(BOT_TOKEN_Ramu5, message);
    }
    public static void sendMessageRamu6(String message) {
        sendTelegramMessage(BOT_TOKEN_Ramu6, message);
    }
    public static void sendMessageRamu7(String message) {
        sendTelegramMessage(BOT_TOKEN_Ramu7, message);
    }

    public static void sendMessageDivi1(String message) {
        sendTelegramMessage(BOT_TOKEN_Divi1, message);
    }

    public static void sendMessageDivi2(String message) {
        sendTelegramMessage(BOT_TOKEN_Divi2, message);
    }
    public static void sendMessageDivi3(String message) {
        sendTelegramMessage(BOT_TOKEN_Divi3, message);
    }
    public static void sendMessageDivi4(String message) {
        sendTelegramMessage(BOT_TOKEN_Divi4, message);
    }
    public static void sendMessageDivi5(String message) {
        sendTelegramMessage(BOT_TOKEN_Divi5, message);
    }
    public static void sendMessageDivi6(String message) {
        sendTelegramMessage(BOT_TOKEN_Divi6, message);
    }
    public static void sendMessageDivi7(String message) {
        sendTelegramMessage(BOT_TOKEN_Divi7, message);
    }
    public static void sendMessageDivi8(String message) {
        sendTelegramMessage(BOT_TOKEN_Divi8, message);
    }
    public static void sendMessageDivi9(String message) {
        sendTelegramMessage(BOT_TOKEN_Divi9, message);
    }
    public static void sendMessageDivi10(String message) {
        sendTelegramMessage(BOT_TOKEN_Divi10, message);
    }

    private static void sendTelegramMessage(String botToken, String message) {
        try {
            URL url = new URL("https://api.telegram.org/bot" + botToken + "/sendMessage");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();

            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setRequestProperty("Content-Type", "application/json");

            String json = String.format("{\"chat_id\": \"%s\", \"text\": \"%s\"}", CHAT_ID, message);

            try (OutputStreamWriter writer = new OutputStreamWriter(conn.getOutputStream())) {
                writer.write(json);
                writer.flush();
            }

            int responseCode = conn.getResponseCode();
            // Optionally handle response code if needed

            conn.disconnect();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


}
