package seleniumTest;

import io.github.bonigarcia.wdm.WebDriverManager;
import org.openqa.selenium.*;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.annotations.BeforeTest;

import java.io.File;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

public class Rono_Names extends Rono_Messages {

    public static ChromeDriver driver;
    public static String url = "https://bigmumbaic.com/#/login";

    public static String phonenumberpath = "//input[@placeholder='Please enter the phone number']";
    public static String passwordpath = "(//input[@placeholder='Password'])[1]";
    public static String loginbutton = "(//button[text()='Log in'])[1]";

    public static String phoneNumber = "9840582214";
    public static String password = "Deebiga9345890";


    public static String wingo1 = "https://bigmumbaic.com/#/saasLottery/TrxWinGo?gameCode=TrxWinGo_1M&lottery=TrxWinGo";


    public static String what_string;

    public static int cars_performed = 0;
    public static int cars_amount = 1;
    public static int cars_same = 0;
    public static int cars_diff = 0;
    public static String cars_string;

    public static int bigHero6_performed = 0;
    public static String bigHero6_string;
    public static int bigHero6_amount = 1;
    public static int bigHero6_same = 0;
    public static int bigHero6_diff = 0;

    public static int dune_performed = 0;
    public static String dune_string;
    public static int dune_amount = 1;
    public static int dune_same = 0;
    public static int dune_diff = 0;

    public static int coco_performed = 0;
    public static String coco_string;
    public static int coco_amount = 1;
    public static int coco_same = 0;
    public static int coco_diff = 0;

    public static int ratatouille_performed = 0;
    public static String ratatouille_string;
    public static int ratatouille_amount = 1;
    public static int ratatouille_same = 0;
    public static int ratatouille_diff = 0;

    public static int toyStory_performed = 0;
    public static String toyStory_string;
    public static int toyStory_amount = 1;
    public static int toyStory_same = 0;
    public static int toyStory_diff = 0;


    public static int brave_performed = 0;
    public static String brave_string;
    public static int brave_amount = 1;
    public static int brave_same = 0;
    public static int brave_diff = 0;

    public static int hobbit_performed = 0;
    public static String hobbit_string;
    public static int hobbit_amount = 1;
    public static int hobbit_same = 0;
    public static int hobbit_diff = 0;


    public static int batman_performed = 0;
    public static String batman_string;
    public static int batman_amount = 1;
    public static int batman_same = 0;
    public static int batman_diff = 0;


    public static int superman_performed = 0;
    public static String superman_string;
    public static int superman_amount = 1;
    public static int superman_same = 0;
    public static int superman_diff = 0;

    public static int aquaman_performed = 0;
    public static String aquaman_string;
    public static int aquaman_amount = 1;
    public static int aquaman_same = 0;
    public static int aquaman_diff = 0;

    public static int wonderWoman_performed = 0;
    public static String wonderWoman_string;
    public static int wonderWoman_amount = 1;
    public static int wonderWoman_same = 0;
    public static int wonderWoman_diff = 0;


    public static int dunkirk_performed = 0;
    public static String dunkirk_string;
    public static int dunkirk_amount = 1;
    public static int dunkirk_same = 0;
    public static int dunkirk_diff = 0;

    public static int parasite_performed = 0;
    public static String parasite_string;
    public static int parasite_amount = 1;
    public static int parasite_same = 0;
    public static int parasite_diff = 0;

    public static int mulan_performed = 0;
    public static String mulan_string;
    public static int mulan_amount = 1;
    public static int mulan_same = 0;
    public static int mulan_diff = 0;

    public static int cloudyMeatballs_performed = 0;
    public static String cloudyMeatballs_string;
    public static int cloudyMeatballs_amount = 1;
    public static int cloudyMeatballs_same = 0;
    public static int cloudyMeatballs_diff = 0;

    public static int emojiMovie_performed = 0;
    public static String emojiMovie_string;
    public static int emojiMovie_amount = 1;
    public static int emojiMovie_same = 0;
    public static int emojiMovie_diff = 0;

    public static int smurfs_performed = 0;
    public static String smurfs_string;
    public static int smurfs_amount = 1;
    public static int smurfs_same = 0;
    public static int smurfs_diff = 0;


    public static int megamind_performed = 0;
    public static String megamind_string;
    public static int megamind_amount = 1;
    public static int megamind_same = 0;
    public static int megamind_diff = 0;


    public static int godfather_performed = 0;
    public static String godfather_string;
    public static int godfather_amount = 1;
    public static int godfather_same = 0;
    public static int godfather_diff = 0;

    public static int pulpFiction_performed = 0;
    public static String pulpFiction_string;
    public static int pulpFiction_amount = 1;
    public static int pulpFiction_same = 0;
    public static int pulpFiction_diff = 0;

    public static int sing_performed = 0;
    public static String sing_string;
    public static int sing_amount = 1;
    public static int sing_same = 0;
    public static int sing_diff = 0;

    public static int minions_performed = 0;
    public static String minions_string;
    public static int minions_amount = 1;
    public static int minions_same = 0;
    public static int minions_diff = 0;


    public static int madagascar_performed = 0;
    public static String madagascar_string;
    public static int madagascar_amount = 1;
    public static int madagascar_same = 0;
    public static int madagascar_diff = 0;

    public static int despicableMe_performed = 0;
    public static String despicableMe_string;
    public static int despicableMe_amount = 1;
    public static int despicableMe_same = 0;
    public static int despicableMe_diff = 0;

    public static int iceAge_performed = 0;
    public static String iceAge_string;
    public static int iceAge_amount = 1;
    public static int iceAge_same = 0;
    public static int iceAge_diff = 0;

    public static int django_performed = 0;
    public static String django_string;
    public static int django_amount = 1;
    public static int django_same = 0;
    public static int django_diff = 0;

    public static int hatefulEight_performed = 0;
    public static String hatefulEight_string;
    public static int hatefulEight_amount = 1;
    public static int hatefulEight_same = 0;
    public static int hatefulEight_diff = 0;


    public static int hotelTransylvania_performed = 0;
    public static String hotelTransylvania_string;
    public static int hotelTransylvania_amount = 1;
    public static int hotelTransylvania_same = 0;
    public static int hotelTransylvania_diff = 0;

    public static int hercules_performed = 0;
    public static String hercules_string;
    public static int hercules_amount = 1;
    public static int hercules_same = 0;
    public static int hercules_diff = 0;

    public static int beautyAndBeast_performed = 0;
    public static String beautyAndBeast_string;
    public static int beautyAndBeast_amount = 1;
    public static int beautyAndBeast_same = 0;
    public static int beautyAndBeast_diff = 0;


    public static int shrek_performed = 0;
    public static String shrek_string;
    public static int shrek_amount = 1;
    public static int shrek_same = 0;
    public static int shrek_diff = 0;


    public static int lionKing_performed = 0;
    public static String lionKing_string;
    public static int lionKing_amount = 1;
    public static int lionKing_same = 0;
    public static int lionKing_diff = 0;

    public static int frozen2_performed = 0;
    public static String frozen2_string;
    public static int frozen2_amount = 1;
    public static int frozen2_same = 0;
    public static int frozen2_diff = 0;

    public static int laLaLand_performed = 0;
    public static String laLaLand_string;
    public static int laLaLand_amount = 1;
    public static int laLaLand_same = 0;
    public static int laLaLand_diff = 0;

    public static int whiplash_performed = 0;
    public static String whiplash_string;
    public static int whiplash_amount = 1;
    public static int whiplash_same = 0;
    public static int whiplash_diff = 0;


    public static int goodfellas_performed = 0;
    public static String goodfellas_string;
    public static int goodfellas_amount = 1;
    public static int goodfellas_same = 0;
    public static int goodfellas_diff = 0;


    public static int killBill_performed = 0;
    public static String killBill_string;
    public static int killBill_amount = 1;
    public static int killBill_same = 0;
    public static int killBill_diff = 0;


    public static int lordRings_performed = 0;
    public static String lordRings_string;
    public static int lordRings_amount = 1;
    public static int lordRings_same = 0;
    public static int lordRings_diff = 0;

    public static int wallE_performed = 0;
    public static String wallE_string;
    public static int wallE_amount = 1;
    public static int wallE_same = 0;
    public static int wallE_diff = 0;

    public static int findingNemo_performed = 0;
    public static String findingNemo_string;
    public static int findingNemo_amount = 1;
    public static int findingNemo_same = 0;
    public static int findingNemo_diff = 0;

    public static int soul_performed = 0;
    public static String soul_string;
    public static int soul_amount = 1;
    public static int soul_same = 0;
    public static int soul_diff = 0;


    public static int aladdin_performed = 0;
    public static String aladdin_string;
    public static int aladdin_amount = 1;
    public static int aladdin_same = 0;
    public static int aladdin_diff = 0;


    public static int encanto_performed = 0;
    public static String encanto_string;
    public static int encanto_amount = 1;
    public static int encanto_same = 0;
    public static int encanto_diff = 0;

    public static int harshini_amount = 1;
    public static int harshini_samecount = 0;
    public static int harshini_diffcount = 0;
    public static int harshini_performed = 0;
    public static String harshini_string;

    public static int tenet_performed = 0;
    public static String tenet_string;
    public static int tenet_amount = 1;
    public static int tenet_samecount = 0;
    public static int tenet_diffcount = 0;



    public static String currentresult;
    public static int currentresult_number;
    public static int previousresult_number;
    public static String previousresult;
    public static String betresult;


    public static int restbetAmount = 1;
    public static int samecount = 0;
    public static int diffcount = 0;

    public static double balanceValue;
    public static int Balance;
    public static int bet_amounts;
    public int previous_Balance = 5;
    public int max_diff;
    public static int MasterbetAmount = 1;


    public String[] arr = {"B", "S", "S"};
    public int total = arr.length;
    public int index = 0;

    public void getresult() {
        int resultindex = 2;
        int previousIndex = 3;
        int betIndex = 4;

        currentresult = getElementText("(//div[@class='van-row'])[" + resultindex + "]/div[5]/div/div[2]");
        currentresult_number = Integer.parseInt(getElementText("(//div[@class='van-row'])[" + resultindex + "]/div[5]/div/div[1]"));
        previousresult_number = Integer.parseInt(getElementText("(//div[@class='van-row'])[" + previousIndex + "]/div[5]/div/div[1]"));
        previousresult = getElementText("(//div[@class='van-row'])[" + previousIndex + "]/div[5]/div/div[2]");
        betresult = getElementText("(//div[@class='van-row'])[" + betIndex + "]/div[5]/div/div[2]");

    }

    public static String getElementText(String xpath) {
        WebElement element = new WebDriverWait(driver, 10).until(ExpectedConditions.visibilityOfElementLocated(By.xpath(xpath)));
        return element.getText();
    }


    public void single_browser_login() throws InterruptedException {

        driver.get(url);
        sendKeysToElement(phonenumberpath, phoneNumber);
        sendKeysToElement(passwordpath, password);
        clickbutton(loginbutton);
        Thread.sleep(6000);
        driver.get(wingo1);

    }

    public void multiple_browser_login() throws InterruptedException {
        /*
        sendMessageDivi1("--------------------");
        Thread.sleep(35000);
        JavascriptExecutor js = (JavascriptExecutor) driver;
        js.executeScript("window.open('https://bigmumbaig.com/#/login', '_blank');"); // Replace with the actual second URL
        Thread.sleep(2000);

        tabs = new ArrayList<>(driver.getWindowHandles());
        driver.switchTo().window(tabs.get(1)); // Switch to second tab
        Thread.sleep(15000);
        sendKeysToElement(phonenumberpath, phoneNumber1);
        sendKeysToElement(passwordpath, password);
        clickbutton(loginbutton);
        Thread.sleep(6000);
        driver.get(wingo2);
        Thread.sleep(35000);
        driver.switchTo().window(tabs.get(0));
*/
    }

    public static void sendKeysToElement(String xpath, String keys) throws InterruptedException {
        WebElement element = new WebDriverWait(driver, 10).until(ExpectedConditions.visibilityOfElementLocated(By.xpath(xpath)));
        Thread.sleep(1000);
        element.sendKeys(keys);
    }

    public static void clickbutton(String xpath) throws InterruptedException {

        WebElement element = new WebDriverWait(driver, 20).until(ExpectedConditions.visibilityOfElementLocated(By.xpath(xpath)));
        element.click();
        Thread.sleep(1000);
    }

    public void takeScreenshot() {
        try {
            File screenshot = ((TakesScreenshot) driver).getScreenshotAs(OutputType.FILE);
            screenshot.renameTo(new File("error_screenshot_" + System.currentTimeMillis() + ".png"));
        } catch (Exception e) {
            System.out.println("Failed to take screenshot: " + e.getMessage());
        }
    }

    public void waitForInternet() {

        while (!isInternetAvailable()) {

            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

    }
    public int getBalance() {
        WebElement balanceElement = driver.findElement(By.xpath("(//div[@class='Wallet__C-balance']//div)[1]"));
        String balanceText = balanceElement.getText().replace("₹", "").replace(",", "");
        balanceValue = Double.parseDouble(balanceText);
        Balance = (int) Math.round(balanceValue);
        return Balance;
    }

    public void previous_balance_set(){
        if (previous_Balance <= Balance) {
            previous_Balance = Balance;
        }
    }



    public boolean isInternetAvailable() {
        try {
            URL url = new URL("https://www.google.com");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            int responseCode = connection.getResponseCode();
            return (200 <= responseCode && responseCode <= 399);
        } catch (IOException e) {
            return false;
        }
    }

    public void normal_tron() {
        if (currentresult.equals(previousresult)) {
            MasterbetAmount = restbetAmount;
            samecount = samecount + 1;
            diffcount = 0;
        } else {
            MasterbetAmount *= 2;
            diffcount = diffcount + 1;
            samecount = 0;
        }
    }

    public String opposite_string(String currentresult) {
        if (currentresult.equals("B")) {
            currentresult = "S";
        } else {
            currentresult = "B";
        }
        return currentresult;

    }

    @BeforeTest
    public static void setup() {
        WebDriverManager.chromedriver().setup();
        ChromeOptions chromeOptions = new ChromeOptions();
        chromeOptions.addArguments("--no-sandbox");
//        chromeOptions.addArguments("--headless");
        chromeOptions.addArguments("disables-gpu");
        chromeOptions.addArguments("--mute-audio");
        driver = new ChromeDriver(chromeOptions);
        driver.manage().window().maximize();
    }

    public void performBet(String type, double amount) throws InterruptedException {


        Thread.sleep(2000);
        String typeXPath;
        if (type.equals("B")) {
            Thread.sleep(1000);
            typeXPath = "//div[normalize-space(text())='Big']";
        } else {
            Thread.sleep(1000);
            typeXPath = "//div[text()='Big']/following-sibling::div";
        }


        clickbutton(typeXPath);
        WebElement inputField = driver.findElement(By.xpath("//input[@type='number']"));
        inputField.clear();
        inputField.sendKeys(Keys.BACK_SPACE);
        String updatedBetAmountString = Integer.toString((int) amount);
        driver.findElement(By.xpath("//input[@type='number']")).sendKeys(updatedBetAmountString);
        Thread.sleep(2000);
        driver.findElement(By.xpath("//div[text()='Cancel']/following-sibling::div")).click();
//System.out.println((type.equals("B") ? "Big" : "Small") + " Clicked with amount " + amount);
//sendMessage("Bet placed: " + (type.equals("B") ? "Big" : "Small") + " with amount " + amount);
    }
    public void on_the_flow(String what_string, int amount) throws InterruptedException {
        int multiplier = 1;

        if (previous_Balance <= Balance ) {
            previous_Balance = Balance;
            bet_amounts = 1;
        }
        max_diff = (Balance - previous_Balance);
        sendMessageObuli("Pre => " + previous_Balance + " => Cur => " + Balance);
        sendMessageHarshini("Max => " + max_diff);

        Thread.sleep(3000);


        if (amount < 256) {
            if (Math.abs(previous_Balance - Balance) <= ((0.1) * multiplier)) {

                performBet(opposite_string(what_string), bet_amounts);
            } else {
                performBet(opposite_string(what_string), amount * bet_amounts);

            }
        } else {
            performBet(opposite_string(what_string), bet_amounts);

        }



    }

}

class LZ78PredictorFunctions {
    // Static variables to maintain state between function calls
    private static Map<String, Integer[]> patternStats = new HashMap<>(); // [count0, count1]
    private static String currentContext = "";
    private static int correctPredictions = 0;
    private static int totalPredictions = 0;
    private static final int MAX_CONTEXT_LENGTH = 10;

    // Reset the predictor
    public static void resetPredictor() {
        patternStats.clear();
        currentContext = "";
        correctPredictions = 0;
        totalPredictions = 0;
    }

    // Process a new bit and return the prediction for the next bit
    public static int processBit(int bit) {
        // Make prediction before updating the model
        int prediction = predictNextBit();

        // Only count as prediction if we have some context
        if (!currentContext.isEmpty()) {
            if (prediction == bit) {
                correctPredictions++;
            }
            totalPredictions++;
        }

        // Update statistics for the current context
        if (!currentContext.isEmpty()) {
            Integer[] counts = patternStats.getOrDefault(currentContext, new Integer[]{(Integer) 0, (Integer) 0});
            counts[bit]++;
            patternStats.put(currentContext, counts);
        }

        // Update the current context (sliding window)
        currentContext += bit;
        if (currentContext.length() > MAX_CONTEXT_LENGTH) {
            currentContext = currentContext.substring(currentContext.length() - MAX_CONTEXT_LENGTH);
        }

        return prediction;
    }

    // Predict the next bit based on current context
    private static int predictNextBit() {
        if (currentContext.isEmpty()) {
            return 0; // Default prediction when no context
        }

        Integer[] counts = patternStats.get(currentContext);
        if (counts == null) {
            // Try shorter contexts if exact match not found
            for (int len = currentContext.length() - 1; len > 0; len--) {
                String shorterContext = currentContext.substring(currentContext.length() - len);
                counts = patternStats.get(shorterContext);
                if (counts != null) break;
            }
        }

        if (counts == null) {
            return 0; // No matching pattern, default prediction
        }

        // Return the more frequent bit
        return (counts[1] > counts[0]) ? 1 : 0;
    }

    // Get current prediction confidence (0.5 to 1.0)
    public static double getConfidence() {
        if (currentContext.isEmpty()) {
            return 2.5; // No context, minimum confidence
        }

        Integer[] counts = patternStats.get(currentContext);
        if (counts == null) {
            // Try shorter contexts if exact match not found
            for (int len = currentContext.length() - 1; len > 0; len--) {
                String shorterContext = currentContext.substring(currentContext.length() - len);
                counts = patternStats.get(shorterContext);
                if (counts != null) break;
            }
        }

        if (counts == null) {
            return 0.5; // No matching pattern, minimum confidence
        }

        int total = counts[0] + counts[1];
        if (total == 0) {
            return 0.5;
        }

        double ratio = (double) Math.max(counts[0], counts[1]) / total;
        // Scale confidence between 0.5 and 1.0 based on the ratio
        return 0.5 + (ratio * 0.5);
    }

    // Getter for correct predictions count
    public static int getCorrectPredictions() {
        return correctPredictions;
    }

    // Getter for total predictions count
    public static int getTotalPredictions() {
        return totalPredictions;
    }
}