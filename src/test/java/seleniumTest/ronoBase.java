package seleniumTest;

import org.testng.annotations.Test;

import java.time.LocalTime;

public class ronoBase extends Rono_Base_condition {

    @Test
    public void everymin() throws InterruptedException {

        single_browser_login();

        try {

            while (true) {
                try {
                    LocalTime now = LocalTime.now();
                    int currentMinute = now.getMinute();
                    int currentSecond = now.getSecond();

                    waitForInternet();
                    if ((currentSecond >= 1 && currentSecond <= 5)) {

                        driver.navigate().refresh();
                        Thread.sleep(8000);

                        getresult();
                        checkStatus();
                        getBalance();
                        previous_balance_set();
                        normal_tron();


                        shrek_string = arr[index];
                        shrek_performed = 1;
                        sendMessageDivi1("Current => " + shrek_string + " => " + shrek_amount);


                        lionKing_string = (shrek_amount == 1) ? opposite_string(shrek_string) : shrek_string;
                        lionKing_performed = 1;
                        sendMessageDivi2("Current => " + lionKing_string + " => " + lionKing_amount);

                        frozen2_string = (lionKing_amount == 1) ? opposite_string(lionKing_string) : lionKing_string;
                        frozen2_performed = 1;
                        sendMessageDivi3("Current => " + frozen2_string + " => " + frozen2_amount);


                        if(frozen2_amount < 8){
                            harshini_string = frozen2_string;
                            harshini_performed = 1;
                            on_the_flow(harshini_string, harshini_amount);
                            sendMessageDivi4("Current => " + harshini_string + " => " + harshini_amount);
                        }






                        layer();


                    }


                    Thread.sleep(2000);
                } catch (Exception e) {
                    e.printStackTrace();
                    takeScreenshot();
                    sendMessage("Error occurred: " + e.getMessage());
                }

            }

        } catch (Exception e) {
            e.printStackTrace();
            takeScreenshot();
            sendMessage("Critical error occurred: " + e.getMessage());
            throw new RuntimeException(e);
        }

    }

}