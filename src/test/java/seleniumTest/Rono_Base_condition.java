package seleniumTest;

public class Rono_Base_condition extends Rono_CheckStatus {

    public void layer() throws InterruptedException {
        what_string = (harshini_string);

        if (harshini_samecount == 1) {
            cars_string = what_string;
            cars_performed = 1;
            lay1();
            sendMessagealbin("Current =>" + cars_string + " => " + cars_amount);
        }

        if (harshini_amount == 2) {
            bigHero6_string = what_string;
            bigHero6_performed = 1;
            lay2();
            sendMessage2("Current =>" + bigHero6_string + " => " + bigHero6_amount);
        }

        if (harshini_amount == 4) {
            dune_string = what_string;
            dune_performed = 1;
            lay4();
            sendMessage4("Current =>" + dune_string + " => " + dune_amount);
            if (max_diff > 20) {
                lordRings_string = dune_string;
                lordRings_performed = 1;
                performBet(lordRings_string, lordRings_amount);
                sendMessageRamu5("Current => " + lordRings_string + " => " + lordRings_amount);
            }

        }

        if (harshini_amount == 8) {
            coco_string = what_string;
            coco_performed = 1;
            lay8();
            sendMessage8("Current =>" + coco_string + " => " + coco_amount);
        }

        if (harshini_amount == 16) {
            ratatouille_string = what_string;
            ratatouille_performed = 1;
            lay16();
            sendMessage16("Current =>" + ratatouille_string + " => " + ratatouille_amount);
        }

        if (harshini_amount == 32) {
            toyStory_string = what_string;
            toyStory_performed = 1;
            sendMessage32("Current =>" + toyStory_string + " => " + toyStory_amount);
        }


    }

    private void lay1() {

        if (cars_amount == 1) {
            brave_string = what_string;
            brave_performed = 1;
            sendMessagehar1("Current =>" + brave_string + " => " + brave_amount);

        }

        if (cars_amount == 2) {
            hobbit_string = what_string;
            hobbit_performed = 1;
            sendMessagehar2("Current =>" + hobbit_string + " => " + hobbit_amount);

        }

        if (cars_amount == 4) {
            batman_string = what_string;
            batman_performed = 1;
            sendMessagehar4("Current =>" + batman_string + " => " + batman_amount);

        }

        if (cars_amount == 8) {
            superman_string = what_string;
            superman_performed = 1;
            sendMessagehar8("Current =>" + superman_string + " => " + superman_amount);

        }

        if (cars_amount == 16) {
            aquaman_string = what_string;
            aquaman_performed = 1;
            sendMessagehar16("Current =>" + aquaman_string + " => " + aquaman_amount);

        }
    }


    private void lay2() {
        if (bigHero6_amount == 1) {
            wonderWoman_string = what_string;
            wonderWoman_performed = 1;
            sendMessagekis1("Current =>" + wonderWoman_string + " => " + wonderWoman_amount);
            kis1();
        }
        if (bigHero6_amount == 2) {
            dunkirk_string = what_string;
            dunkirk_performed = 1;
            sendMessagekis2("Current =>" + dunkirk_string + " => " + dunkirk_amount);

        }
        if (bigHero6_amount == 4) {
            parasite_string = what_string;
            parasite_performed = 1;
            sendMessagekis4("Current =>" + parasite_string + " => " + parasite_amount);

        }
        if (bigHero6_amount == 8) {
            mulan_string = what_string;
            mulan_performed = 1;
            sendMessagekis8("Current =>" + mulan_string + " => " + mulan_amount);

        }
        if (bigHero6_amount == 16) {
            cloudyMeatballs_string = what_string;
            cloudyMeatballs_performed = 1;
            sendMessagekis16("Current =>" + cloudyMeatballs_string + " => " + cloudyMeatballs_amount);

        }
    }

    private void kis1() {

        if (wonderWoman_amount == 1) {
            laLaLand_string = "B";
            laLaLand_performed = 1;
            sendMessageRamu1("Current => " + laLaLand_string + " => " + laLaLand_amount);

            if (laLaLand_amount == 1) {
                whiplash_string = wonderWoman_string;
                whiplash_performed = 1;
                sendMessageRamu2("Current => " + whiplash_string + " => " + whiplash_amount);

                if (whiplash_amount == 1) {
                    goodfellas_string = whiplash_string;
                    goodfellas_performed = 1;
                    sendMessageRamu3("Current => " + goodfellas_string + " => " + goodfellas_amount);
                    if (goodfellas_amount == 1) {
                        killBill_string = goodfellas_string;
                        killBill_performed = 1;
                        sendMessageRamu4("Current => " + killBill_string + " => " + killBill_amount);

                    }

                }

            }


        }

    }

    private void lay4() {
        if (dune_amount == 1) {
            emojiMovie_string = what_string;
            emojiMovie_performed = 1;
            sendMessagelast2("Current =>" + emojiMovie_string + " => " + emojiMovie_amount);

        }
        if (dune_amount == 2) {
            smurfs_string = what_string;
            smurfs_performed = 1;
            sendMessagelast4("Current =>" + smurfs_string + " => " + smurfs_amount);

        }
        if (dune_amount == 4) {
            megamind_string = what_string;
            megamind_performed = 1;
            sendMessagelast8("Current =>" + megamind_string + " => " + megamind_amount);

        }
        if (dune_amount == 8) {
            godfather_string = what_string;
            godfather_performed = 1;
            sendMessagelast16("Current =>" + godfather_string + " => " + godfather_amount);

        }
        if (dune_amount == 16) {
            pulpFiction_string = what_string;
            pulpFiction_performed = 1;
            sendMessagelast32("Current =>" + pulpFiction_string + " => " + pulpFiction_amount);

        }
    }

    private void lay8() {
        if (coco_amount == 1) {
            sing_string = what_string;
            sing_performed = 1;
            sendMessageobuli1("Current =>" + sing_string + " => " + sing_amount);
        }

        if (coco_amount == 2) {
            minions_string = what_string;
            minions_performed = 1;
            sendMessageobuli2("Current =>" + minions_string + " => " + minions_amount);
        }
        if (coco_amount == 4) {
            madagascar_string = what_string;
            madagascar_performed = 1;
            sendMessageobuli4("Current =>" + madagascar_string + " => " + madagascar_amount);
        }
        if (coco_amount == 8) {
            despicableMe_string = what_string;
            despicableMe_performed = 1;
            sendMessageobuli8("Current =>" + despicableMe_string + " => " + despicableMe_amount);
        }
        if (coco_amount == 16) {
            iceAge_string = what_string;
            iceAge_performed = 1;
            sendMessageobuli16("Current =>" + iceAge_string + " => " + iceAge_amount);
        }

    }

    private void lay16() {
        if (ratatouille_amount == 1) {
            django_string = what_string;
            django_performed = 1;
            sendMessagePadma1("Current =>" + django_string + " => " + django_amount);
        }
        if (ratatouille_amount == 2) {
            hatefulEight_string = what_string;
            hatefulEight_performed = 1;
            sendMessagePadma2("Current =>" + hatefulEight_string + " => " + hatefulEight_amount);
        }
        if (ratatouille_amount == 4) {
            hotelTransylvania_string = what_string;
            hotelTransylvania_performed = 1;
            sendMessagePadma4("Current =>" + hotelTransylvania_string + " => " + hotelTransylvania_amount);
        }
        if (ratatouille_amount == 8) {
            hercules_string = what_string;
            hercules_performed = 1;
            sendMessagePadma8("Current =>" + hercules_string + " => " + hercules_amount);
        }
        if (ratatouille_amount == 16) {
            beautyAndBeast_string = what_string;
            beautyAndBeast_performed = 1;
            sendMessagePadma16("Current =>" + beautyAndBeast_string + " => " + beautyAndBeast_amount);
        }

    }


}

